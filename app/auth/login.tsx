import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Leaf, Mail, Lock, Eye, EyeOff } from 'lucide-react-native';
import * as AuthSession from 'expo-auth-session';
import { Colors } from '@/constants/colors';
import { Button } from '@/components/ui/Button';
import { supabase } from '@/lib/supabase';
import { OAuthDebug } from '@/components/debug/OAuthDebug';

interface LoginScreenProps {
  onSuccess: () => void;
}

export default function LoginScreen({ onSuccess }: LoginScreenProps) {
  const [isLogin, setIsLogin] = useState(true);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [showDebug, setShowDebug] = useState(false);

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePassword = (password: string) => {
    if (password.length < 6) return false;

    // Check for at least one lowercase letter
    if (!/[a-z]/.test(password)) return false;

    // Check for at least one uppercase letter
    if (!/[A-Z]/.test(password)) return false;

    // Check for at least one digit
    if (!/[0-9]/.test(password)) return false;

    // Check for at least one special character
    if (!/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password)) return false;

    return true;
  };

  const getPasswordValidationErrors = (password: string) => {
    const errors = [];

    if (password.length < 6) {
      errors.push('At least 6 characters');
    }
    if (!/[a-z]/.test(password)) {
      errors.push('One lowercase letter (a-z)');
    }
    if (!/[A-Z]/.test(password)) {
      errors.push('One uppercase letter (A-Z)');
    }
    if (!/[0-9]/.test(password)) {
      errors.push('One digit (0-9)');
    }
    if (!/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password)) {
      errors.push('One special character (!@#$%^&*...)');
    }

    return errors;
  };

  const handleAuth = async () => {
    if (!validateEmail(email)) {
      Alert.alert('Invalid Email', 'Please enter a valid email address.');
      return;
    }

    if (!validatePassword(password)) {
      const errors = getPasswordValidationErrors(password);
      Alert.alert(
        'Password Requirements Not Met',
        `Your password must include:\n• ${errors.join('\n• ')}`
      );
      return;
    }

    if (!isLogin && password !== confirmPassword) {
      Alert.alert('Password Mismatch', 'Passwords do not match.');
      return;
    }

    setLoading(true);

    try {
      if (isLogin) {
        const { error } = await supabase.auth.signInWithPassword({
          email,
          password,
        });

        if (error) {
          Alert.alert('Login Failed', error.message);
        } else {
          onSuccess();
        }
      } else {
        const { data, error } = await supabase.auth.signUp({
          email,
          password,
        });

        if (error) {
          console.error('Signup error:', error);
          let errorMessage = error.message;

          // Handle specific error cases
          if (error.status === 422) {
            errorMessage = 'Invalid email address or password format. Please check your input and try again.';
          } else if (error.message.includes('already registered')) {
            errorMessage = 'This email is already registered. Please try logging in instead.';
          } else if (error.message.includes('password')) {
            errorMessage = 'Password does not meet security requirements. Please ensure it includes uppercase, lowercase, numbers, and special characters.';
          }

          Alert.alert('Sign Up Failed', errorMessage);
        } else {
          // console.log('Signup successful:', data);
          if (data.user && !data.user.email_confirmed_at) {
            Alert.alert(
              'Account Created Successfully!',
              'Please check your email address to confirm your account before signing in. Look for an email from PlantConnects and click the confirmation link.',
              [{ text: 'OK', onPress: () => setIsLogin(true) }]
            );
          } else {
            Alert.alert(
              'Account Created Successfully!',
              'Your PlantConnects account has been created and is ready to use!',
              [{ text: 'OK', onPress: () => setIsLogin(true) }]
            );
          }
        }
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleAuth = async () => {
    setLoading(true);
    try {
      console.log('Starting Google OAuth with AuthSession...');

      // Use AuthSession for better Expo Go compatibility
      // In development (Expo Go), use Expo's proxy. In production, use custom scheme.
      const redirectUri = __DEV__
        ? AuthSession.makeRedirectUri({
            // For development with Expo Go, don't specify scheme - uses Expo's proxy
            path: '/--/auth/callback',
          })
        : AuthSession.makeRedirectUri({
            scheme: 'plantconnects',
            path: 'auth/callback',
          });

      console.log('Redirect URI:', redirectUri);
      console.log('Development mode:', __DEV__);
      console.log('Platform:', Platform.OS);

      // Generate the OAuth URL using Supabase
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: redirectUri,
          skipBrowserRedirect: true,
        },
      });

      if (error) {
        console.error('Google OAuth error:', error);
        Alert.alert('Google Sign In Failed', error.message);
        return;
      }

      console.log('OAuth URL generated:', data.url);

      // For mobile platforms, handle the OAuth URL
      if (Platform.OS !== 'web' && data.url) {
        try {
          // Import WebBrowser dynamically to avoid web issues
          const WebBrowserModule = await import('expo-web-browser');
          const WebBrowser = WebBrowserModule.default || WebBrowserModule;

          // Check if WebBrowser has the required method
          if (WebBrowser && typeof WebBrowser.openAuthSessionAsync === 'function') {
            console.log('Opening auth session with URL:', data.url);
            console.log('Redirect URL:', redirectUri);

            // Configure WebBrowser for better compatibility
            await WebBrowser.warmUpAsync();

            const result = await WebBrowser.openAuthSessionAsync(
              data.url,
              redirectUri,
              {
                showInRecents: false,
                createTask: false,
              }
            );

            console.log('Auth session result:', result);

            if (result.type === 'success' && result.url) {
              console.log('Full callback URL:', result.url);

              // Extract the session from the callback URL
              const url = new URL(result.url);
              const access_token = url.searchParams.get('access_token');
              const refresh_token = url.searchParams.get('refresh_token');
              const error_param = url.searchParams.get('error');
              const error_description = url.searchParams.get('error_description');

              console.log('Callback URL params:', {
                access_token: access_token ? 'present' : 'missing',
                refresh_token: refresh_token ? 'present' : 'missing',
                error: error_param,
                error_description,
                fullUrl: result.url
              });

              if (error_param) {
                Alert.alert('Authentication Error', error_description || error_param);
                return;
              }

              if (access_token && refresh_token) {
                console.log('Setting session with tokens...');
                const { error: sessionError } = await supabase.auth.setSession({
                  access_token,
                  refresh_token,
                });

                if (sessionError) {
                  console.error('Session error:', sessionError);
                  Alert.alert('Authentication Error', sessionError.message);
                } else {
                  console.log('Authentication successful');
                  onSuccess();
                }
              } else {
                console.error('Missing tokens in callback URL');
                Alert.alert('Authentication Error', 'Missing authentication tokens in callback');
              }
            } else if (result.type === 'cancel') {
              console.log('User cancelled authentication');
            } else {
              console.log('Authentication failed or was dismissed:', result);
            }

            await WebBrowser.coolDownAsync();
          } else {
            throw new Error('WebBrowser.openAuthSessionAsync is not available');
          }
        } catch (webBrowserError) {
          console.error('WebBrowser error:', webBrowserError);
          Alert.alert('Error', 'Unable to open authentication browser. Please try again.');
        }
      }
      // For web, the redirect will be handled automatically
    } catch (error) {
      console.error('Google OAuth exception:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = async () => {
    if (!validateEmail(email)) {
      Alert.alert('Invalid Email', 'Please enter a valid email address to reset your password.');
      return;
    }

    setLoading(true);
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: __DEV__ 
          ? 'https://auth.expo.io/@geoattract/app-plantconnects-290725/reset-password'
          : 'plantconnects://reset-password',
      });

      if (error) {
        Alert.alert('Reset Password Failed', error.message);
      } else {
        Alert.alert(
          'Reset Password',
          'Check your email for a password reset link.',
          [{ text: 'OK', onPress: () => setShowForgotPassword(false) }]
        );
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <LinearGradient
      colors={[Colors.primary, Colors.primaryDark]}
      style={styles.container}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView contentContainerStyle={styles.scrollContent}>
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.iconContainer}>
              <Leaf size={50} color={Colors.background} />
            </View>
            <Text style={styles.title}>PlantConnects</Text>
            <Text style={styles.subtitle}>
              {isLogin ? 'Welcome back!' : 'Join our community'}
            </Text>
          </View>

          {/* Form */}
          <View style={styles.form}>
            <View style={styles.inputContainer}>
              <Mail size={20} color={Colors.textMuted} style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Email address"
                placeholderTextColor={Colors.textMuted}
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>

            <View style={styles.inputContainer}>
              <Lock size={20} color={Colors.textMuted} style={styles.inputIcon} />
              <TextInput
                style={styles.input}
                placeholder="Password"
                placeholderTextColor={Colors.textMuted}
                value={password}
                onChangeText={setPassword}
                secureTextEntry={!showPassword}
                autoCapitalize="none"
              />
              <TouchableOpacity
                onPress={() => setShowPassword(!showPassword)}
                style={styles.eyeIcon}
              >
                {showPassword ? (
                  <EyeOff size={20} color={Colors.textMuted} />
                ) : (
                  <Eye size={20} color={Colors.textMuted} />
                )}
              </TouchableOpacity>
            </View>

            {/* Password Requirements Helper Text */}
            {!isLogin && password.length > 0 && (
              <View style={styles.passwordRequirements}>
                <Text style={styles.passwordRequirementsTitle}>Password must include:</Text>
                {getPasswordValidationErrors(password).map((error, index) => (
                  <Text key={index} style={styles.passwordRequirementItem}>
                    • {error}
                  </Text>
                ))}
                {validatePassword(password) && (
                  <Text style={styles.passwordRequirementSuccess}>
                    ✓ Password meets all requirements
                  </Text>
                )}
              </View>
            )}

            {!isLogin && (
              <View style={styles.inputContainer}>
                <Lock size={20} color={Colors.textMuted} style={styles.inputIcon} />
                <TextInput
                  style={styles.input}
                  placeholder="Confirm password"
                  placeholderTextColor={Colors.textMuted}
                  value={confirmPassword}
                  onChangeText={setConfirmPassword}
                  secureTextEntry={!showPassword}
                  autoCapitalize="none"
                />
              </View>
            )}

            <Button
              title={isLogin ? 'Sign In' : 'Create Account'}
              onPress={handleAuth}
              loading={loading}
              style={styles.authButton}
              textStyle={styles.authButtonText}
            />

            {isLogin && (
              <TouchableOpacity
                onPress={() => setShowForgotPassword(true)}
                style={styles.forgotPasswordButton}
              >
                <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
              </TouchableOpacity>
            )}

            <View style={styles.divider}>
              <View style={styles.dividerLine} />
              <Text style={styles.dividerText}>or</Text>
              <View style={styles.dividerLine} />
            </View>

            <Button
              title="Continue with Google"
              onPress={handleGoogleAuth}
              variant="outline"
              loading={loading}
              style={styles.googleButton}
              textStyle={styles.googleButtonText}
            />

            <TouchableOpacity
              onPress={() => setIsLogin(!isLogin)}
              style={styles.switchButton}
            >
              <Text style={styles.switchText}>
                {isLogin
                  ? "Don't have an account? Sign up"
                  : 'Already have an account? Sign in'}
              </Text>
            </TouchableOpacity>

            {/* Debug Button - Remove this in production */}
            <TouchableOpacity
              onPress={() => setShowDebug(true)}
              style={styles.debugButton}
            >
              <Text style={styles.debugButtonText}>Debug OAuth</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Forgot Password Modal */}
      {showForgotPassword && (
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Reset Password</Text>
            <Text style={styles.modalText}>
              Enter your email address and we'll send you a link to reset your password.
            </Text>

            <View style={styles.modalInputContainer}>
              <Mail size={20} color={Colors.textMuted} style={styles.inputIcon} />
              <TextInput
                style={styles.modalInput}
                placeholder="Email address"
                placeholderTextColor={Colors.textMuted}
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>

            <View style={styles.modalButtons}>
              <TouchableOpacity
                onPress={() => setShowForgotPassword(false)}
                style={[styles.modalButton, styles.cancelButton]}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={handleForgotPassword}
                style={[styles.modalButton, styles.resetButton]}
                disabled={loading}
              >
                <Text style={styles.resetButtonText}>
                  {loading ? 'Sending...' : 'Send Reset Link'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}

      {/* Debug Modal - Remove this in production */}
      {showDebug && (
        <View style={styles.debugModalOverlay}>
          <OAuthDebug onClose={() => setShowDebug(false)} />
        </View>
      )}
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: 30,
    paddingVertical: 40,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.background,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
  },
  form: {
    width: '100%',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 16,
  },
  inputIcon: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: Colors.text,
  },
  eyeIcon: {
    padding: 4,
  },
  passwordRequirements: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  passwordRequirementsTitle: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  passwordRequirementItem: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 12,
    marginBottom: 4,
  },
  passwordRequirementSuccess: {
    color: '#4CAF50',
    fontSize: 12,
    fontWeight: '600',
    marginTop: 4,
  },
  authButton: {
    backgroundColor: Colors.background,
    marginTop: 8,
    marginBottom: 24,
  },
  authButtonText: {
    color: Colors.primary,
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  dividerText: {
    color: 'rgba(255, 255, 255, 0.8)',
    paddingHorizontal: 16,
    fontSize: 14,
  },
  googleButton: {
    backgroundColor: Colors.background,
    borderColor: Colors.background,
    marginBottom: 24,
  },
  googleButtonText: {
    color: Colors.text,
  },
  switchButton: {
    alignItems: 'center',
    paddingVertical: 12,
  },
  switchText: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 16,
  },
  forgotPasswordButton: {
    alignItems: 'center',
    paddingVertical: 8,
    marginBottom: 16,
  },
  forgotPasswordText: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 14,
    textDecorationLine: 'underline',
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 30,
  },
  modalContent: {
    backgroundColor: Colors.background,
    borderRadius: 16,
    padding: 24,
    width: '100%',
    maxWidth: 400,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 12,
    textAlign: 'center',
  },
  modalText: {
    fontSize: 14,
    color: Colors.textMuted,
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 20,
  },
  modalInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.cardBackground,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  modalInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.text,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: Colors.cardBackground,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  cancelButtonText: {
    color: Colors.textMuted,
    fontSize: 16,
    fontWeight: '500',
  },
  resetButton: {
    backgroundColor: Colors.primary,
  },
  resetButtonText: {
    color: Colors.background,
    fontSize: 16,
    fontWeight: '600',
  },
  debugButton: {
    alignItems: 'center',
    paddingVertical: 8,
    marginTop: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 8,
  },
  debugButtonText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12,
  },
  debugModalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
  },
});
