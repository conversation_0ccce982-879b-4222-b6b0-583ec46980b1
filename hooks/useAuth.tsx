import React, { createContext, useContext, useEffect, useState } from 'react';
import { Session, User } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signOut: () => Promise<void>;
  refreshSession: () => Promise<boolean>;
  isSessionValid: () => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session }, error }) => {
      if (error) {
        console.error('Error getting session:', error);
      }
      // Removed console.log for initial session to reduce noise
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event, session) => {
      // Only log meaningful auth events, not initial session checks
      if (event !== 'INITIAL_SESSION') {
        console.log('Auth state change:', event, session ? 'authenticated' : 'not authenticated');
      }
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    if (error) {
      throw error;
    }
  };

  const refreshSession = async (): Promise<boolean> => {
    try {
      const { data, error } = await supabase.auth.refreshSession();
      if (error) {
        console.error('Error refreshing session:', error);
        return false;
      }

      if (data.session) {
        setSession(data.session);
        setUser(data.session.user);
        return true;
      }

      return false;
    } catch (error) {
      console.error('Exception refreshing session:', error);
      return false;
    }
  };

  const isSessionValid = (): boolean => {
    if (!session) return false;

    // Check if session is expired (with 5 minute buffer)
    const expiresAt = session.expires_at;
    if (!expiresAt) return true; // If no expiry, assume valid

    const now = Math.floor(Date.now() / 1000);
    const buffer = 5 * 60; // 5 minutes in seconds

    return expiresAt > (now + buffer);
  };

  const value = {
    user,
    session,
    loading,
    signOut,
    refreshSession,
    isSessionValid,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
