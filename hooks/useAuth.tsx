import React, { createContext, useContext, useEffect, useState } from 'react';
import { useUser, useAuth as useClerkAuth } from '@clerk/clerk-expo';
import { createClerkSupabaseClient } from '@/lib/supabase';

// Define a simplified user interface that matches what the app expects
interface AppUser {
  id: string;
  email?: string;
  user_metadata?: {
    full_name?: string;
    avatar_url?: string;
    preferred_username?: string;
  };
}

interface AuthContextType {
  user: AppUser | null;
  session: any | null; // Keep for compatibility but will be null
  loading: boolean;
  signOut: () => Promise<void>;
  refreshSession: () => Promise<boolean>;
  isSessionValid: () => boolean;
  getSupabaseClient: () => Promise<any>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { user: clerkUser, isLoaded } = useUser();
  const { signOut: clerkSignOut, getToken } = useClerkAuth();
  const [user, setUser] = useState<AppUser | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!isLoaded) {
      setLoading(true);
      return;
    }

    if (clerkUser) {
      // Convert Clerk user to our app user format
      const appUser: AppUser = {
        id: clerkUser.id,
        email: clerkUser.primaryEmailAddress?.emailAddress,
        user_metadata: {
          full_name: clerkUser.fullName || undefined,
          avatar_url: clerkUser.imageUrl || undefined,
          preferred_username: clerkUser.username || undefined,
        },
      };
      setUser(appUser);
    } else {
      setUser(null);
    }

    setLoading(false);
  }, [clerkUser, isLoaded]);

  const signOut = async () => {
    try {
      await clerkSignOut();
    } catch (error) {
      console.error('Error signing out:', error);
      throw error;
    }
  };

  const refreshSession = async (): Promise<boolean> => {
    // With Clerk, tokens are automatically refreshed
    // Just return true if user is authenticated
    return !!clerkUser;
  };

  const isSessionValid = (): boolean => {
    return !!clerkUser;
  };

  const getSupabaseClient = async () => {
    return await createClerkSupabaseClient(async () => {
      try {
        return await getToken({ template: 'supabase' });
      } catch (error) {
        console.error('Error getting Clerk token:', error);
        return null;
      }
    });
  };

  const value = {
    user,
    session: null, // Keep for compatibility but always null with Clerk
    loading,
    signOut,
    refreshSession,
    isSessionValid,
    getSupabaseClient,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
