import React from 'react';
import { StyleSheet, Text, View, Image, ScrollView, Alert, TouchableOpacity } from 'react-native'; // Added Alert and TouchableOpacity
import { Check, AlertCircle, X } from 'lucide-react-native';
import { Colors } from '@/constants/colors';
import { IdentificationResult } from '@/types/plant';
import { Button } from '@/components/ui/Button';
import { CareIndicator } from '@/components/ui/CareIndicator';
import { Card } from '@/components/ui/Card';
import { DatabaseService } from '@/services/database'; // Import DatabaseService
import { useAuth } from '@/hooks/useAuth'; // Import useAuth
import { useIdentification } from '@/hooks/useIdentificationStore'; // Import useIdentification

interface IdentificationResultViewProps {
  result: IdentificationResult;
  onNewScan: () => void;
  onClose?: () => void; // Add optional close handler
  // onAddToGarden and onKeepToMyself will be handled internally
}

export const IdentificationResultView: React.FC<IdentificationResultViewProps> = ({
  result,
  onNewScan,
  onClose,
}) => {
  const { plant, confidence, imageUri, diagnosisData, identificationData } = result; // Use diagnosisData and identificationData
  const isHighConfidence = confidence >= 0.9;
  const { user } = useAuth();
  const { refreshRecentIdentifications, refreshRecentDiagnoses, uploadImageToStorage } = useIdentification(); // Use functions from store

  // Check if the result is not a plant
  const isNotPlant = plant.commonName === 'Not a plant' || plant.scientificName === 'Not a plant';

  const handleSaveIdentification = async (isPublic: boolean) => {
    if (!user) {
      Alert.alert('Error', 'You must be logged in to save identifications.');
      return;
    }

    try {
      // Upload image to storage
      const finalImageUrl = await uploadImageToStorage(imageUri, diagnosisData ? 'diagnosis' : 'scan');

      let savedIdentification = null;
      let savedDiagnosis = null;

      // Check if it's a diagnosis result
      if (diagnosisData) {
        // Create plant identification first
        const plantIdentification = await DatabaseService.createPlantIdentification({
          user_id: user.id,
          image_url: finalImageUrl,
          scientific_name: identificationData.scientificName,
          common_name: identificationData.commonName,
          description: identificationData.description,
          care_instructions: JSON.stringify(identificationData.careInstructions),
          tags: identificationData.tags,
          confidence_score: identificationData.confidence,
          identification_source: 'ai',
          is_verified: false,
          is_public: isPublic, // Set public status based on button
          location_taken: undefined, // Add location if available
          plant_type: identificationData.plantType,
          native_region: identificationData.nativeRegion,
          toxicity_level: identificationData.toxicity?.level,
          toxicity_warning: identificationData.toxicity?.warning,
          growth_habit: identificationData.growthHabit,
          growth_rate: identificationData.growthRate,
          mature_height: identificationData.matureSize?.height,
          mature_width: identificationData.matureSize?.width,
          mature_description: identificationData.matureSize?.description,
          bloom_time: identificationData.bloomTime,
          flower_colors: identificationData.flowerColor,
          foliage_type: identificationData.foliageType,
          hardiness_zones: identificationData.hardiness?.zones,
          min_temperature: identificationData.hardiness?.minTemperature,
          pests_and_diseases: identificationData.additionalInfo?.pestsAndDiseases,
          fun_facts: identificationData.additionalInfo?.funFacts,
          uses: identificationData.additionalInfo?.uses,
          propagation: identificationData.additionalInfo?.propagation,
          seasonal_care: identificationData.additionalInfo?.seasonalCare,
          companion_plants: identificationData.additionalInfo?.companionPlants,
          maintenance_level: identificationData.additionalInfo?.maintenanceLevel,
        });

        savedIdentification = plantIdentification;
        // Then create diagnosis
          const normalizedSeverity = diagnosisData.severity?.toLowerCase() as 'mild' | 'moderate' | 'severe' | 'critical';
          savedDiagnosis = await DatabaseService.createPlantDiagnosis({
            user_id: user.id,
            plant_identification_id: plantIdentification.id,
            image_url: finalImageUrl,
            problem_description: result.problemDescription,
            diagnosed_problem: diagnosisData.diagnosedProblem,
            likely_causes: diagnosisData.likelyCauses,
            symptoms_observed: diagnosisData.symptomsObserved,
            severity: normalizedSeverity,
            immediate_actions: diagnosisData.immediateActions,
            long_term_care: diagnosisData.longTermCare,
            product_recommendations: diagnosisData.productRecommendations,
            step_by_step_instructions: diagnosisData.stepByStepInstructions,
            prevention_tips: diagnosisData.preventionTips,
            prognosis: diagnosisData.prognosis,
            confidence_score: diagnosisData.confidence,
            diagnosis_source: 'ai',
            is_verified: false,
            is_public: isPublic, // Set public status based on button
          });
      } else {
        // It's a regular identification
        savedIdentification = await DatabaseService.createPlantIdentification({
          user_id: user.id,
          image_url: finalImageUrl,
          scientific_name: identificationData.scientificName,
          common_name: identificationData.commonName,
          description: identificationData.description,
          care_instructions: JSON.stringify(identificationData.careInstructions),
          tags: identificationData.tags,
          confidence_score: identificationData.confidence,
          identification_source: 'ai',
          is_verified: false,
          is_public: isPublic, // Set public status based on button
          location_taken: undefined, // Add location if available
          plant_type: identificationData.plantType,
          native_region: identificationData.nativeRegion,
          toxicity_level: identificationData.toxicity?.level,
          toxicity_warning: identificationData.toxicity?.warning,
          growth_habit: identificationData.growthHabit,
          growth_rate: identificationData.growthRate,
          mature_height: identificationData.matureSize?.height,
          mature_width: identificationData.matureSize?.width,
          mature_description: identificationData.matureSize?.description,
          bloom_time: identificationData.bloomTime,
          flower_colors: identificationData.flowerColor,
          foliage_type: identificationData.foliageType,
          hardiness_zones: identificationData.hardiness?.zones,
          min_temperature: identificationData.hardiness?.minTemperature,
          pests_and_diseases: identificationData.additionalInfo?.pestsAndDiseases,
          fun_facts: identificationData.additionalInfo?.funFacts,
          uses: identificationData.additionalInfo?.uses,
          propagation: identificationData.additionalInfo?.propagation,
          seasonal_care: identificationData.additionalInfo?.seasonalCare,
          companion_plants: identificationData.additionalInfo?.companionPlants,
          maintenance_level: identificationData.additionalInfo?.maintenanceLevel,
        });
      }

      Alert.alert('Success', `Plant ${diagnosisData ? 'diagnosis' : 'identification'} saved successfully!`);
      // Refresh recent lists and update user stats
      refreshRecentIdentifications();
      refreshRecentDiagnoses();
      DatabaseService.updateUserProfileStats(user.id);
    } catch (error: any) {
      console.error('Error saving identification:', error);
      // Check for unique constraint violation
      if (error.message.includes('duplicate key value violates unique constraint')) {
        Alert.alert('Already Saved', 'This plant identification has already been saved.');
      } else {
        Alert.alert('Error', `Failed to save plant identification: ${error.message}`);
      }
    }
  };

  return (
    <ScrollView style={styles.container} testID="identification-result">
      {/* Close button */}
      {onClose && (
        <TouchableOpacity
          style={styles.closeButton}
          onPress={onClose}
          testID="close-result-button"
        >
          <X size={24} color={Colors.text} />
        </TouchableOpacity>
      )}

      <View style={styles.imageContainer}>
        <Image source={{ uri: imageUri }} style={styles.image} />
        <View style={[styles.confidenceBadge, isHighConfidence ? styles.highConfidence : styles.lowConfidence]}>
          {isHighConfidence ? (
            <Check size={16} color={Colors.background} />
          ) : (
            <AlertCircle size={16} color={Colors.background} />
          )}
          <Text style={styles.confidenceText}>{Math.round(confidence * 100)}% Match</Text>
        </View>
      </View>

      <View style={styles.content}>
        <Text style={styles.commonName}>{plant.commonName}</Text>
        <Text style={styles.scientificName}>{plant.scientificName}</Text>
        
        <Text style={styles.description}>{plant.description}</Text>
        
        <Text style={styles.sectionTitle}>Care Instructions</Text>
        <Card style={styles.careCard}>
          <CareIndicator type="light" level={plant.careInstructions.light} />
          <CareIndicator type="water" level={plant.careInstructions.water} />
          <CareIndicator type="humidity" level={plant.careInstructions.humidity} />
          
          <View style={styles.careDetail}>
            <Text style={styles.careLabel}>Temperature:</Text>
            <Text style={styles.careValue}>
              {plant.careInstructions?.temperature?.min || 18}° - {plant.careInstructions?.temperature?.max || 25}°{plant.careInstructions?.temperature?.unit || 'C'}
            </Text>
          </View>
          
          <View style={styles.careDetail}>
            <Text style={styles.careLabel}>Soil:</Text>
            <Text style={styles.careValue}>{plant.careInstructions.soil}</Text>
          </View>
          
          <View style={styles.careDetail}>
            <Text style={styles.careLabel}>Fertilizer:</Text>
            <Text style={styles.careValue}>{plant.careInstructions.fertilizer}</Text>
          </View>
          
          <View style={styles.careDetail}>
            <Text style={styles.careLabel}>Toxicity:</Text>
            <Text style={styles.careValue}>
              {plant.careInstructions.toxicity.charAt(0).toUpperCase() + plant.careInstructions.toxicity.slice(1)}
            </Text>
          </View>
        </Card>

        {diagnosisData && ( // Use diagnosisData to conditionally render
          <>
            <Text style={styles.sectionTitle}>Health Diagnosis</Text>
            <Card style={styles.diagnosisCard}>
              <Text style={styles.diagnosisText}>{diagnosisData.diagnosedProblem}</Text>
              <Text style={styles.diagnosisText}>Severity: {diagnosisData.severity}</Text>
              <Text style={styles.diagnosisText}>Symptoms: {diagnosisData.symptomsObserved}</Text>
              <Text style={styles.diagnosisText}>Causes: {diagnosisData.likelyCauses?.join(', ')}</Text>
              <Text style={styles.diagnosisText}>Prognosis: {diagnosisData.prognosis}</Text>
            </Card>
            <Text style={styles.sectionTitle}>Treatment Recommendations</Text>
            <Card style={styles.treatmentCard}>
              <Text style={styles.treatmentText}>Immediate Actions: {diagnosisData.immediateActions?.join(', ')}</Text>
              <Text style={styles.treatmentText}>Long-term Care: {diagnosisData.longTermCare?.join(', ')}</Text>
              <Text style={styles.treatmentText}>Product Recommendations: {diagnosisData.productRecommendations?.join(', ')}</Text>
              <Text style={styles.treatmentText}>Step-by-step: {diagnosisData.stepByStepInstructions?.join(', ')}</Text>
              <Text style={styles.treatmentText}>Prevention: {diagnosisData.preventionTips?.join(', ')}</Text>
            </Card>
          </>
        )}

        <View style={styles.actions}>
          {isNotPlant ? (
            // Show only "Retake again" button for non-plant results
            <Button
              title="Retake again"
              variant="outline"
              onPress={onNewScan}
              style={styles.newScanButton}
              testID="retake-button"
            />
          ) : (
            // Show all three buttons for plant results
            <>
              <Button
                title="Add to my Garden & Showcase this Discovery"
                onPress={() => handleSaveIdentification(true)} // Pass true for public
                style={styles.addButton}
                testID="add-to-garden-button"
              />
              <Button
                title="No! Keep this to myself"
                onPress={() => handleSaveIdentification(false)} // Pass false for private
                variant="outline"
                style={styles.newScanButton} // Reusing style, consider renaming
                testID="keep-to-myself-button"
              />
              <Button
                title="New Scan"
                variant="outline"
                onPress={onNewScan}
                style={styles.newScanButton}
                testID="new-scan-button"
              />
            </>
          )}
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  imageContainer: {
    position: 'relative',
  },
  image: {
    width: '100%',
    height: 300,
  },
  confidenceBadge: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  highConfidence: {
    backgroundColor: Colors.primary,
  },
  lowConfidence: {
    backgroundColor: Colors.error,
  },
  confidenceText: {
    color: Colors.background,
    fontWeight: '600',
    fontSize: 14,
    marginLeft: 4,
  },
  content: {
    padding: 20,
  },
  commonName: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.text,
    marginBottom: 4,
  },
  scientificName: {
    fontSize: 16,
    fontStyle: 'italic',
    color: Colors.textLight,
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    color: Colors.text,
    lineHeight: 24,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 12,
  },
  careCard: {
    marginBottom: 24,
  },
  careDetail: {
    flexDirection: 'row',
    marginVertical: 4,
    paddingLeft: 44,
  },
  careLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text,
    width: 100,
  },
  careValue: {
    fontSize: 14,
    color: Colors.textLight,
    flex: 1,
  },
  actions: {
    marginTop: 8,
  },
  addButton: {
    marginBottom: 12,
    minHeight: 64,
    paddingVertical: 16,
    paddingHorizontal: 24,
  },
  newScanButton: {
    marginBottom: 24,
  },
  diagnosisCard: {
    marginBottom: 24,
    backgroundColor: '#FFF3CD',
    borderColor: '#FFEAA7',
  },
  diagnosisText: {
    fontSize: 14,
    color: '#856404',
    lineHeight: 20,
  },
  treatmentCard: {
    marginBottom: 24,
    backgroundColor: '#D1ECF1',
    borderColor: '#BEE5EB',
  },
  treatmentText: {
    fontSize: 14,
    color: '#0C5460',
    lineHeight: 20,
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    zIndex: 10,
    backgroundColor: Colors.background,
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
});
