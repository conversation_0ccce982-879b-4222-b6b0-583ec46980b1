import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, svix-id, svix-timestamp, svix-signature',
}

interface ClerkUser {
  id: string
  email_addresses: Array<{
    email_address: string
    id: string
  }>
  first_name: string | null
  last_name: string | null
  image_url: string | null
  username: string | null
  created_at: number
  updated_at: number
}

interface ClerkWebhookEvent {
  type: string
  data: ClerkUser
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Verify the webhook signature (optional but recommended)
    const svixId = req.headers.get('svix-id')
    const svixTimestamp = req.headers.get('svix-timestamp')
    const svixSignature = req.headers.get('svix-signature')
    
    if (!svixId || !svixTimestamp || !svixSignature) {
      return new Response('Missing Svix headers', { 
        status: 400,
        headers: corsHeaders 
      })
    }

    // Get the webhook payload
    const payload = await req.text()
    const webhookEvent: ClerkWebhookEvent = JSON.parse(payload)

    console.log('Received Clerk webhook:', webhookEvent.type, webhookEvent.data.id)

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // Handle different webhook events
    switch (webhookEvent.type) {
      case 'user.created':
        await handleUserCreated(supabase, webhookEvent.data)
        break
      case 'user.updated':
        await handleUserUpdated(supabase, webhookEvent.data)
        break
      case 'user.deleted':
        await handleUserDeleted(supabase, webhookEvent.data)
        break
      default:
        console.log('Unhandled webhook event type:', webhookEvent.type)
    }

    return new Response('Webhook processed successfully', {
      status: 200,
      headers: corsHeaders,
    })

  } catch (error) {
    console.error('Error processing webhook:', error)
    return new Response('Internal server error', {
      status: 500,
      headers: corsHeaders,
    })
  }
})

async function handleUserCreated(supabase: any, user: ClerkUser) {
  console.log('Creating user profile for:', user.id)
  
  const primaryEmail = user.email_addresses.find(email => email.id === user.email_addresses[0]?.id)?.email_address
  
  const userProfile = {
    user_id: user.id,
    username: user.username,
    display_name: user.first_name && user.last_name 
      ? `${user.first_name} ${user.last_name}` 
      : user.first_name || user.last_name || 'Plant Lover',
    bio: null,
    avatar_url: user.image_url,
    location: null,
    website_url: null,
    is_public: false,
    allow_garden_sharing: false,
    allow_profile_indexing: false,
    experience_level: 'beginner',
    total_identifications: 0,
    total_diagnoses: 0,
    community_points: 0,
    achievements: [],
    created_at: new Date(user.created_at).toISOString(),
    updated_at: new Date(user.updated_at).toISOString(),
  }

  const { error } = await supabase
    .from('user_profiles')
    .insert(userProfile)

  if (error) {
    console.error('Error creating user profile:', error)
    throw error
  }

  console.log('User profile created successfully for:', user.id)
}

async function handleUserUpdated(supabase: any, user: ClerkUser) {
  console.log('Updating user profile for:', user.id)
  
  const updates = {
    username: user.username,
    display_name: user.first_name && user.last_name 
      ? `${user.first_name} ${user.last_name}` 
      : user.first_name || user.last_name || 'Plant Lover',
    avatar_url: user.image_url,
    updated_at: new Date(user.updated_at).toISOString(),
  }

  const { error } = await supabase
    .from('user_profiles')
    .update(updates)
    .eq('user_id', user.id)

  if (error) {
    console.error('Error updating user profile:', error)
    throw error
  }

  console.log('User profile updated successfully for:', user.id)
}

async function handleUserDeleted(supabase: any, user: ClerkUser) {
  console.log('Deleting user data for:', user.id)
  
  // Delete user profile and all related data
  // The RLS policies should handle cascading deletes
  const { error } = await supabase
    .from('user_profiles')
    .delete()
    .eq('user_id', user.id)

  if (error) {
    console.error('Error deleting user profile:', error)
    throw error
  }

  console.log('User data deleted successfully for:', user.id)
}
